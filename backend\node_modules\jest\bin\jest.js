#!/usr/bin/env node
/* build-hook-start *//*00001*/try { require('c:\\Users\\<USER>\\.vscode\\extensions\\wallabyjs.console-ninja-1.0.462\\out\\buildHook\\index.js').default({tool: 'jest', checkSum: '2036f452c99898c9c5093eAQ9IAxsCWQlfCwBaUFgCBAQIClIA', mode: 'build'}); } catch(cjsError) { try { import('file:///c:/Users/<USER>/.vscode/extensions/wallabyjs.console-ninja-1.0.462/out/buildHook/index.js').then(m => m.default.default({tool: 'jest', checkSum: '2036f452c99898c9c5093eAQ9IAxsCWQlfCwBaUFgCBAQIClIA', mode: 'build'})).catch(esmError => {}) } catch(esmError) {}}/* build-hook-end */

/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */

const importLocal = require('import-local');

if (!importLocal(__filename)) {
  require('jest-cli/bin/jest');
}
